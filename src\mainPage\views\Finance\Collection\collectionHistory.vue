<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
    <template #menu="{ row }">
        <el-button type="primary" text icon="view" @click="viewDetail(row)">明细</el-button>
        <!-- <el-button type="primary" text icon="Back" @click="back(row)">撤回</el-button> -->
      </template>
      <template #collectionFiles="{ row }">
        <File :fileList="row.attachList || []"></File>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer title="收款详情" size="80%" v-model="drawer">
      <historyDetail :actualIds="currentIds"></historyDetail>
    </el-drawer>
  </basic-container>
</template>

<script setup name="CollectionHistory">
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import historyDetail from './historyDetail.vue';
let option = ref({
  height: 'auto',
  align: 'center',
  header: false,
  addBtn: true,
  editBtn: false,
  delBtn: false,
  menu: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 6,
  searchLabelWidth: 120,
  labelWidth: 140,
  menuWidth: 100,
  border: true,
  column: [
    // {
    //   label: '关联合同',
    //   prop: 'contractName',
    //   overHidden: true,
    //   width: 200,
    //   search: true,
    //   hide:true
    //   //   component: 'wf-contract-select',
    // },

    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true,
      component: 'wf-customer-drop',
      search: true,
      // width: 150,
    },

    // {
    //   type: 'input',
    //   label: '计划名称',
    //   span: 12,
    //   display: true,
    //   prop: 'planName',
    //   required: true,
    //   overHidden: true,
    //   rules: [
    //     {
    //       required: true,
    //       message: '计划名称必须填写',
    //     },
    //   ],
    // },
    {
      type: 'number',
      label: '计划收款金额',
      controls: true,
      span: 24,
      addDisplay: false,
      editDisplay: false,
      prop: 'planCollectionPrice',
    },
    {
      type: 'number',
      label: '本次收款金额',
      controls: true,
      span: 24,
      addDisplay: false,
      editDisplay: false,
      prop: 'actualPrice',
    },
    {
      type: 'number',
      label: '本次收款比例',
      controls: true,
      span: 24,
      addDisplay: false,
      editDisplay: false,
      prop: 'actualCollectionRate',
      formatter:row => {
        return parseFloat(row.actualCollectionRate) + '%'
      }
    },

    {
      type: 'date',
      label: '本次收款时间',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD',
      prop: 'actualDate',
      component: 'wf-daterange-search',
      search: true,
      disabled: false,
      readonly: false,
      required: true,
      rules: [
        {
          required: true,
          message: '计划收款时间必须填写',
        },
      ],
    },
    {
        type: 'number',
        label: '本次收款账号',
        controls: true,
        span: 24,
        addDisplay: false,
        editDisplay: false,
        prop: 'collectionAccountName',
      },
    {
      label: '收款凭证',
      prop: 'collectionFiles',
      type: 'upload',
      dataType: 'object',
      //   listType: 'picture-img',
      loadText: '图片上传中，请稍等',
      span: 24,
      slot: true,
      limit: 1,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
      },
      //   formatter: (row) => {
      //     return row.attachList[0].link
      //   },
      action: '/blade-resource/attach/upload',
      //   uploadAfter: (res, done) => {
      //     imgId.value = res.id;
      //     done();
      //   },
    },
    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContractActualCollection/conformityPage';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        actualStartTime: params.value.actualDate && params.value.actualDate[0],
        actualEndTime: params.value.actualDate && params.value.actualDate[1],
        actualDate: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function back(row) {
  proxy
    .$confirm('确定退回此收款记录吗', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios.post('/api/vt-admin/sealContractPlanCollection/returnBack?id=' + row.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '退回成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
let drawer = ref(false);
let currentIds = ref('');
function viewDetail(row) {
  currentIds.value = row.actualIds;
  drawer.value = true;
}
</script>

<style lang="scss" scoped></style>
