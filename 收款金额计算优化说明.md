# 收款金额计算优化说明

## 优化概述

针对合同明细中的收款金额显示进行了优化，当没有发票时，本次收款金额现在显示当前行的 `planCollectionPrice`（计划收款金额），而不是合同总金额或产品列表金额。

## 主要改进

### 1. 数据源优先级调整
**优化前：**
- 发票明细：使用产品列表金额求和
- 合同明细：使用合同总金额或产品列表金额

**优化后：**
- 发票明细：使用产品列表金额求和
- 合同明细：**优先使用当前行的 planCollectionPrice**

### 2. 逻辑优化
```javascript
function calculateContractCollectionAmount(contract) {
  // 🆕 如果是合同明细且有当前行数据，使用 planCollectionPrice
  if (detailForm.value.isContractDetail && detailForm.value.currentRowData) {
    return parseFloat(detailForm.value.currentRowData.planCollectionPrice) || 0;
  }
  
  // 发票明细使用 invoiceDetailVOList
  if (contract.invoiceDetailVOList && contract.invoiceDetailVOList.length > 0) {
    return contract.invoiceDetailVOList.reduce((total, product) => {
      const amount = parseFloat(product.totalPrice) || 0;
      return total + amount;
    }, 0);
  }
  
  // 其他情况的降级处理...
}
```

### 3. 数据传递增强
在查询合同详情时，保存当前行数据：
```javascript
detailForm.value = {
  customerName: contractData.customerName,
  customerContactName: contractData.customerContactName,
  offerId: contractData.offerId,
  invoicePriceType: 0,
  sealContractVOList: [contractData],
  isContractDetail: true,
  currentRowData: row // 🆕 保存当前行数据
};
```

## 业务逻辑说明

### planCollectionPrice 的含义
- **字段名称**：`planCollectionPrice`
- **业务含义**：当前收款计划的计划收款金额
- **数据来源**：收款计划表中的具体金额
- **使用场景**：当查看没有发票的收款计划明细时

### 为什么使用 planCollectionPrice？
1. **业务准确性**：反映了当前收款计划的实际金额
2. **数据一致性**：与收款计划表中的金额保持一致
3. **用户期望**：用户查看明细时期望看到与计划金额相符的数值
4. **避免混淆**：避免显示合同总金额造成的误解

## 技术实现

### 数据流程
```
用户点击查看明细
    ↓
检查是否有发票ID
    ↓
没有发票 → 查询合同详情
    ↓
保存当前行数据 (包含 planCollectionPrice)
    ↓
计算收款金额时优先使用 planCollectionPrice
    ↓
显示格式化的金额
```

### 数据源优先级
1. **planCollectionPrice** - 当前行计划收款金额 (合同明细时)
2. **invoiceDetailVOList** - 发票产品列表金额求和 (发票明细时)
3. **productList** - 合同产品列表金额求和 (备选)
4. **contractTotalPrice** - 合同总金额 (备选)
5. **0** - 默认值

### 错误处理
- 使用 `parseFloat()` 确保数值转换的安全性
- 提供默认值 `|| 0` 防止 NaN 错误
- 保持向后兼容性

## 用户体验提升

### 1. 数据准确性
- ✅ 显示与收款计划相符的金额
- ✅ 避免显示不相关的合同总金额
- ✅ 提供准确的业务信息

### 2. 界面一致性
- ✅ 保持与发票明细相同的显示格式
- ✅ 统一的金额格式化处理
- ✅ 一致的用户交互体验

### 3. 业务理解
- ✅ 用户能够清楚地看到当前收款计划的金额
- ✅ 减少业务理解上的歧义
- ✅ 提供更有意义的信息展示

## 文件修改清单

1. **`contractCollectionPlan.vue`** - 合同收款计划明细
   - ✅ 修改 `viewDetail1` 函数，保存当前行数据
   - ✅ 优化 `calculateContractCollectionAmount` 函数
   - ✅ 添加 `planCollectionPrice` 优先级处理

2. **`collectionPlanMy.vue`** - 我的收款计划明细
   - ✅ 应用相同的优化逻辑
   - ✅ 保持功能一致性

## 测试建议

### 测试场景
1. **有发票的收款计划**：验证显示发票产品金额
2. **无发票的收款计划**：验证显示 planCollectionPrice
3. **数据异常情况**：验证错误处理和默认值
4. **界面显示**：验证金额格式化和显示效果

### 验证要点
- 金额计算的准确性
- 数据来源的正确性
- 界面显示的一致性
- 错误处理的健壮性

这次优化使收款金额的显示更加准确和有意义，用户现在可以看到与收款计划直接相关的金额信息，提升了系统的业务准确性和用户体验。
