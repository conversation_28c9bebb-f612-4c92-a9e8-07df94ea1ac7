# 合同编号显示优化说明

## 优化概述

优化了明细对话框中合同标题的显示逻辑，当只有一个合同时不再显示"合同1"、"合同2"这样的编号，使界面更加简洁和自然。

## 主要改进

### 1. 智能编号显示
**优化前：**
- 无论合同数量多少，都显示"合同1"、"合同2"等编号
- 即使只有一个合同也显示"合同1："

**优化后：**
- 只有一个合同时：直接显示合同名称
- 多个合同时：显示"合同1"、"合同2"等编号

### 2. 条件渲染逻辑
```vue
<template v-if="detailForm.sealContractVOList.length > 1">
  合同 {{ index + 1 }}：
</template>
<el-link type="primary" @click="toDetail({ id: item.id })" class="contract-link">
  {{ item.contractName }}
</el-link>
```

### 3. 显示效果对比

#### 单个合同场景
**优化前：**
```
┌─────────────────────────────────────┐
│ 合同1：某某项目建设合同               │
│ 本次收款金额：￥100,000.00          │
└─────────────────────────────────────┘
```

**优化后：**
```
┌─────────────────────────────────────┐
│ 某某项目建设合同                     │
│ 本次收款金额：￥100,000.00          │
└─────────────────────────────────────┘
```

#### 多个合同场景
**保持不变：**
```
┌─────────────────────────────────────┐
│ 合同1：某某项目建设合同               │
│ 本次收款金额：￥100,000.00          │
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│ 合同2：某某设备采购合同               │
│ 本次收款金额：￥50,000.00           │
└─────────────────────────────────────┘
```

## 技术实现

### 判断逻辑
- 使用 `detailForm.sealContractVOList.length > 1` 判断合同数量
- 当合同数量大于1时才显示编号
- 保持合同名称的点击跳转功能不变

### 模板语法
```vue
<span class="header-title">
  <template v-if="detailForm.sealContractVOList.length > 1">
    合同 {{ index + 1 }}：
  </template>
  <el-link type="primary" @click="toDetail({ id: item.id })" class="contract-link">
    {{ item.contractName }}
  </el-link>
</span>
```

### 兼容性考虑
- 保持原有的功能完整性
- 不影响多合同场景的使用
- 保持样式和交互的一致性

## 用户体验提升

### 1. 界面简洁性
- ✅ 减少不必要的编号显示
- ✅ 突出合同名称本身
- ✅ 避免单合同时的冗余信息

### 2. 视觉层次
- ✅ 单合同时更加突出合同名称
- ✅ 多合同时保持清晰的区分
- ✅ 保持一致的视觉风格

### 3. 认知负担
- ✅ 减少用户的认知负担
- ✅ 更符合用户的直觉期望
- ✅ 提升信息获取效率

## 应用场景

### 适用情况
1. **发票明细**：单张发票关联单个合同
2. **合同明细**：查看单个合同的详情
3. **收款计划**：单个收款计划对应单个合同

### 保持编号的情况
1. **批量开票**：一张发票包含多个合同
2. **合并收款**：一次收款涉及多个合同
3. **统一管理**：需要区分多个合同的场景

## 文件修改清单

1. **`contractCollectionPlan.vue`** - 合同收款计划明细
   - ✅ 添加合同数量判断逻辑
   - ✅ 优化合同标题显示

2. **`collectionPlanMy.vue`** - 我的收款计划明细
   - ✅ 应用相同的显示逻辑
   - ✅ 保持功能一致性

3. **`invoiceApply.vue`** - 开票申请明细
   - ✅ 统一显示规则
   - ✅ 保持界面一致性

## 测试建议

### 测试场景
1. **单合同明细**：验证不显示编号
2. **多合同明细**：验证正确显示编号
3. **边界情况**：验证空列表或异常数据的处理
4. **交互功能**：验证合同名称点击跳转功能

### 验证要点
- 编号显示的正确性
- 合同名称的完整显示
- 点击跳转功能的正常工作
- 样式和布局的一致性

## 后续优化建议

1. **动态标题**：考虑根据合同数量动态调整对话框标题
2. **批量操作**：为多合同场景添加批量操作功能
3. **排序功能**：为多合同列表添加排序选项
4. **折叠展开**：考虑为多合同场景添加折叠展开功能

这次优化使界面显示更加智能和简洁，特别是在单合同场景下，用户可以更直观地看到合同信息，提升了整体的用户体验。
