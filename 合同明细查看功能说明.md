# 合同明细查看功能说明

## 功能概述

增强了明细查看功能，当没有发票时，系统会自动查询合同详情并以相同的界面方式展示，为用户提供一致的查看体验。

## 主要改进

### 1. 智能数据源切换
- **有发票时**：调用发票详情API (`/api/vt-admin/sealContractInvoice/detail`)
- **无发票时**：调用合同详情API (`/api/vt-admin/sealContract/detail`)
- **统一展示**：两种数据源使用相同的界面布局和样式

### 2. 数据结构转换
当查询合同详情时，系统会将合同数据转换为与发票详情相同的数据结构：

```javascript
// 合同详情数据转换
const contractData = res.data.data;
detailForm.value = {
  customerName: contractData.customerName,
  customerContactName: contractData.customerContactName,
  invoicePriceType: 0, // 默认按产品开票
  sealContractVOList: [contractData], // 将单个合同包装成数组
  isContractDetail: true // 标识这是合同明细
};
```

### 3. 动态标题显示
- **发票明细**：对话框标题显示"发票明细"
- **合同明细**：对话框标题显示"合同明细"
- **智能判断**：通过 `isContractDetail` 标识自动切换

### 4. 兼容的金额计算
增强了收款金额计算函数，支持多种数据源：

```javascript
function calculateContractCollectionAmount(contract) {
  // 发票明细使用 invoiceDetailVOList
  if (contract.invoiceDetailVOList && contract.invoiceDetailVOList.length > 0) {
    return contract.invoiceDetailVOList.reduce((total, product) => {
      const amount = parseFloat(product.totalPrice) || 0;
      return total + amount;
    }, 0);
  }
  
  // 合同明细使用 productList
  if (contract.productList && contract.productList.length > 0) {
    return contract.productList.reduce((total, product) => {
      const amount = parseFloat(product.totalPrice) || 0;
      return total + amount;
    }, 0);
  }
  
  // 使用合同总金额作为备选
  if (contract.contractTotalPrice) {
    return parseFloat(contract.contractTotalPrice) || 0;
  }
  
  return 0;
}
```

## 技术实现

### API 调用逻辑
```javascript
function viewDetail1(row) {
  if (!row.invoiceId) {
    // 没有发票时查询合同详情
    if (!row.sealContractId) {
      return ElMessage.warning('无法获取合同信息');
    }
    
    axios.get('/api/vt-admin/sealContract/detail?id=' + row.sealContractId)
      .then(res => {
        dialogVisible1.value = true;
        // 数据转换逻辑...
      })
      .catch(err => {
        ElMessage.error('获取合同详情失败');
      });
    return;
  }
  
  // 有发票时查询发票详情
  axios.get('/api/vt-admin/sealContractInvoice/detail?id=' + row.invoiceId)
    .then(res => {
      dialogVisible1.value = true;
      detailForm.value = res.data.data;
    })
    .catch(err => {
      ElMessage.error('获取发票详情失败');
    });
}
```

### 界面展示特点
1. **公共信息区域**：统一显示客户基本信息
2. **合同卡片区域**：
   - 合同名称可点击跳转
   - 显示收款/开票金额
   - 展示合同详细信息
3. **错误处理**：提供友好的错误提示

## 用户体验提升

### 1. 无缝切换
- 用户无需关心数据来源
- 统一的操作方式和界面布局
- 一致的交互体验

### 2. 信息完整性
- 即使没有发票也能查看合同详情
- 保持信息展示的完整性
- 提供必要的操作入口

### 3. 智能提示
- 动态标题提示当前查看的内容类型
- 清晰的错误提示信息
- 合理的降级处理

## 文件修改清单

1. **`contractCollectionPlan.vue`** - 合同收款计划明细
   - ✅ 增强 `viewDetail1` 函数
   - ✅ 添加合同详情查询逻辑
   - ✅ 优化金额计算函数
   - ✅ 动态标题显示

2. **`collectionPlanMy.vue`** - 我的收款计划明细
   - ✅ 应用相同的功能增强
   - ✅ 统一的实现逻辑

## 后续优化建议

1. **缓存机制**：考虑添加数据缓存，避免重复请求
2. **加载状态**：添加加载指示器，提升用户体验
3. **数据验证**：增强数据有效性检查
4. **错误恢复**：提供更多的错误恢复选项

## 兼容性说明

- 保持向后兼容，不影响现有功能
- 新增功能对现有用户透明
- 可以通过配置控制功能开关

这次功能增强显著提升了系统的易用性和完整性，用户现在可以在任何情况下都能查看到相关的明细信息，无论是否关联了发票。
